openapi: 3.0.3
info:
  title: 排课管理系统API接口文档 - 更新版
  description: |
    排课管理系统的API接口文档，包含新增的科目管理功能
    
    ## 更新内容
    1. 课程实体新增科目字段
    2. 新增获取教师主教科目接口
    3. 课程创建和更新接口支持科目字段
    
  version: 2.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  # 新增接口：获取教师主教科目
  /principal/teachers/{teacherId}/subjects:
    get:
      tags:
        - 校长端-教师管理
      summary: 获取指定教师的主教科目列表
      description: 校长获取其名下教师的主教科目列表
      parameters:
        - name: teacherId
          in: path
          required: true
          description: 教师ID
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "获取教师科目成功"
                  data:
                    type: array
                    items:
                      type: string
                    example: ["数学", "物理"]
        '400':
          description: 请求参数错误
        '401':
          description: 未授权
        '403':
          description: 权限不足
        '404':
          description: 教师不存在

  # 更新接口：创建课程（新增科目字段）
  /principal/courses:
    post:
      tags:
        - 校长端-课程管理
      summary: 创建课程（更新版）
      description: 校长创建新课程，支持科目字段
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCourseRequestV2'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "课程创建成功"
                  data:
                    $ref: '#/components/schemas/CourseV2'

  # 更新接口：更新课程（新增科目字段）
  /principal/courses/{courseId}:
    put:
      tags:
        - 校长端-课程管理
      summary: 更新课程信息（更新版）
      description: 校长更新课程信息，支持科目字段
      parameters:
        - name: courseId
          in: path
          required: true
          description: 课程ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCourseRequestV2'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "课程更新成功"
                  data:
                    $ref: '#/components/schemas/CourseV2'

components:
  schemas:
    # 更新版课程实体（新增科目字段）
    CourseV2:
      type: object
      properties:
        courseId:
          type: integer
          description: 课程ID
        schoolId:
          type: integer
          description: 学校ID
        teacherId:
          type: integer
          description: 教师ID
        studentId:
          type: integer
          description: 学生ID
        classroomId:
          type: integer
          description: 教室ID
        courseDate:
          type: string
          format: date
          description: 上课日期
        startTime:
          type: string
          format: time
          description: 开始时间
        endTime:
          type: string
          format: time
          description: 结束时间
        price:
          type: number
          format: decimal
          description: 课程价格
        gradeLevel:
          type: string
          description: 年级
        subject:
          type: string
          description: 科目
          example: "数学"
        status:
          type: string
          enum: [scheduled, completed, cancelled]
          description: 课程状态
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
        teacher:
          $ref: '#/components/schemas/Teacher'
        student:
          $ref: '#/components/schemas/Student'
        classroom:
          $ref: '#/components/schemas/Classroom'

    # 更新版创建课程请求（新增科目字段）
    CreateCourseRequestV2:
      type: object
      required:
        - teacherId
        - studentId
        - classroomId
        - courseDate
        - startTime
        - endTime
        - price
        - gradeLevel
        - subject
      properties:
        teacherId:
          type: integer
          description: 教师ID
        studentId:
          type: integer
          description: 学生ID
        classroomId:
          type: integer
          description: 教室ID
        courseDate:
          type: string
          format: date
          description: 上课日期
        startTime:
          type: string
          format: time
          description: 开始时间
        endTime:
          type: string
          format: time
          description: 结束时间
        price:
          type: number
          format: decimal
          description: 课程价格
        gradeLevel:
          type: string
          description: 年级
        subject:
          type: string
          description: 科目
          example: "数学"
        status:
          type: string
          enum: [scheduled, completed, cancelled]
          default: scheduled
          description: 课程状态

    # 更新版更新课程请求（新增科目字段）
    UpdateCourseRequestV2:
      type: object
      properties:
        teacherId:
          type: integer
          description: 教师ID
        studentId:
          type: integer
          description: 学生ID
        classroomId:
          type: integer
          description: 教室ID
        courseDate:
          type: string
          format: date
          description: 上课日期
        startTime:
          type: string
          format: time
          description: 开始时间
        endTime:
          type: string
          format: time
          description: 结束时间
        price:
          type: number
          format: decimal
          description: 课程价格
        gradeLevel:
          type: string
          description: 年级
        subject:
          type: string
          description: 科目
          example: "数学"
        status:
          type: string
          enum: [scheduled, completed, cancelled]
          description: 课程状态

    # 教师实体（参考）
    Teacher:
      type: object
      properties:
        userId:
          type: integer
          description: 用户ID
        realName:
          type: string
          description: 真实姓名
        phone:
          type: string
          description: 手机号
        email:
          type: string
          description: 邮箱
        subject:
          type: string
          description: 主教科目（逗号分隔）
          example: "数学,物理"
        subjectArray:
          type: array
          items:
            type: string
          description: 主教科目数组
          example: ["数学", "物理"]

    # 学生实体（参考）
    Student:
      type: object
      properties:
        studentId:
          type: integer
          description: 学生ID
        name:
          type: string
          description: 学生姓名
        contactPhone:
          type: string
          description: 联系电话

    # 教室实体（参考）
    Classroom:
      type: object
      properties:
        classroomId:
          type: integer
          description: 教室ID
        name:
          type: string
          description: 教室名称
        floor:
          type: integer
          description: 楼层
        type:
          type: string
          description: 教室类型

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 校长端-教师管理
    description: 校长端教师相关接口
  - name: 校长端-课程管理
    description: 校长端课程相关接口
