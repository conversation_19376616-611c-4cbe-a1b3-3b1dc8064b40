# 排课管理科目功能实现总结

## 需求概述
在排课管理界面的右侧日历视图中，点击空闲时段区域打开新增排课弹框后，新增一项科目选择功能，科目选择列表展示该老师的主教科目列表。

## 实现内容

### 1. 后端修改

#### 1.1 数据库结构修改
- **文件**: `teachingassistant-backend/src/main/resources/db/migration/V2__Add_subject_to_courses.sql`
- **修改**: 在courses表中添加subject字段
- **SQL**: 
  ```sql
  ALTER TABLE `courses` ADD COLUMN `subject` VARCHAR(50) COMMENT '科目' AFTER `grade_level`;
  ```

#### 1.2 实体类修改
- **文件**: `teachingassistant-backend/src/main/java/com/teachingassistant/entity/Course.java`
- **修改**: 添加subject字段
- **代码**:
  ```java
  /**
   * 科目
   */
  private String subject;
  ```

#### 1.3 Mapper修改
- **文件**: `teachingassistant-backend/src/main/resources/mapper/CourseMapper.xml`
- **修改**: 
  - 在结果映射中添加subject字段映射
  - 在BaseColumns中添加subject字段
  - 在insert和update语句中添加subject字段

#### 1.4 新增API接口
- **文件**: `teachingassistant-backend/src/main/java/com/teachingassistant/controller/PrincipalController.java`
- **新增接口**: `GET /principal/teachers/{teacherId}/subjects`
- **功能**: 获取指定教师的主教科目列表
- **权限**: 校长只能查看其名下教师的科目信息

### 2. 前端修改

#### 2.1 API接口定义
- **文件**: `teachingassistant-front/src/api/teacher.ts`
- **新增方法**: `getTeacherSubjects(teacherId: number)`

- **文件**: `teachingassistant-front/src/api/course.ts`
- **修改**: 在CreateCourseData、UpdateCourseData、CourseInfo接口中添加subject字段

#### 2.2 类型定义
- **文件**: `teachingassistant-front/src/types/index.ts`
- **修改**: 在Course接口中添加subject字段

#### 2.3 排课界面修改
- **文件**: `teachingassistant-front/src/views/principal/schedule/index.vue`
- **主要修改**:
  1. 在新增排课表单中添加科目选择字段
  2. 添加teacherSubjects响应式数据
  3. 新增fetchTeacherSubjects方法获取教师科目
  4. 新增handleAddCourseTeacherChange方法处理教师选择变化
  5. 在表单验证规则中添加科目必填验证
  6. 在课程创建时包含科目字段
  7. 添加相关CSS样式

#### 2.4 界面功能
- 选择教师后自动获取该教师的主教科目列表
- 如果教师只有一个科目，自动选择该科目
- 如果教师有多个科目，用户需要手动选择
- 如果教师没有设置科目，显示提示信息
- 科目字段为必填项，有相应的验证提示

### 3. 接口文档更新
- **文件**: `排课管理API接口文档更新.yaml`
- **内容**: 
  - 新增获取教师主教科目接口文档
  - 更新课程相关接口，包含科目字段
  - 提供完整的请求响应示例

## 技术实现细节

### 1. 数据流程
1. 用户在排课界面选择教师
2. 前端调用`/principal/teachers/{teacherId}/subjects`接口获取教师科目
3. 科目列表展示在科目选择下拉框中
4. 用户选择科目后，创建课程时包含科目信息
5. 后端将科目信息保存到courses表的subject字段

### 2. 权限控制
- 校长只能查看其名下教师的科目信息
- 接口会验证教师是否属于当前校长
- 确保数据安全性

### 3. 用户体验优化
- 自动获取科目列表，无需手动输入
- 单科目自动选择，减少操作步骤
- 多科目手动选择，确保准确性
- 友好的提示信息

## 测试验证
- ✅ 前端代码构建成功，无语法错误
- ✅ 后端代码编译成功，无编译错误
- ✅ 数据库迁移脚本语法正确
- ✅ API接口定义完整

## 部署说明

### 1. 数据库迁移
运行数据库迁移脚本，为courses表添加subject字段：
```sql
-- 在数据库中执行
ALTER TABLE `courses` ADD COLUMN `subject` VARCHAR(50) COMMENT '科目' AFTER `grade_level`;
```

### 2. 后端部署
重新编译并部署后端应用，新的API接口将生效。

### 3. 前端部署
重新构建并部署前端应用，新的排课界面将包含科目选择功能。

## 使用说明

### 1. 教师科目设置
确保教师在系统中已设置主教科目，否则排课时无法选择科目。

### 2. 排课流程
1. 进入排课管理界面
2. 切换到"新增排课"模式
3. 选择教室和教师
4. 点击空闲时段打开排课弹框
5. 系统自动获取教师科目列表
6. 选择科目（如果有多个）
7. 填写其他信息并提交

### 3. 注意事项
- 科目字段为必填项
- 只能选择教师的主教科目
- 如果教师没有设置科目，需要先在教师管理中设置

## 后续优化建议
1. 可以考虑在课程列表中显示科目信息
2. 可以添加按科目筛选课程的功能
3. 可以在统计报表中加入科目维度的分析
