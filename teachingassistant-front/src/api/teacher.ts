import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 老师管理相关类型定义
export interface TeacherListParams {
  page?: number
  size?: number
  schoolId?: number
  principalId?: number
  realName?: string
}

export interface UnassignedTeacherListParams {
  page?: number
  size?: number
  realName?: string
}

export interface AssignTeachersData {
  teacherIds: number[]
}

export interface UpdateTeacherData {
  realName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  subject?: string[]  // 支持多选科目
  experience?: number
  bio?: string
  status?: 'active' | 'inactive'
}

export interface AvailableTeachersParams {
  courseDate: string
  startTime: string
  endTime: string
  subject?: string
}

export interface TeacherInfo {
  userId: number
  username: string
  realName: string
  role: string
  schoolId?: number
  schoolName?: string
  principalId?: number
  principalName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  hireDate?: string
  subject?: string | string[]  // 兼容单选和多选
  experience?: number
  bio?: string
  status?: string
  lastLoginAt?: string
  createdAt?: string
  updatedAt?: string
  principal?: {
    userId: number
    realName: string
    phone?: string
    email?: string
  }
}

/**
 * 校长端老师管理API
 */
export const principalTeacherApi = {
  /**
   * 获取未分配校长的老师列表
   */
  getUnassignedTeachers(params: UnassignedTeacherListParams): Promise<ApiResponse<PageResult<TeacherInfo>>> {
    return request.get('/principal/unassigned-teachers', params)
  },

  /**
   * 获取当前校长名下的老师列表
   */
  getMyTeachers(params: UnassignedTeacherListParams): Promise<ApiResponse<PageResult<TeacherInfo>>> {
    return request.get('/principal/teachers', params)
  },

  /**
   * 分配老师给当前校长
   */
  assignTeachers(data: AssignTeachersData): Promise<ApiResponse<void>> {
    return request.post('/principal/assign-teachers', data)
  },

  /**
   * 解除老师与当前校长的关系
   */
  removeTeacher(teacherId: number): Promise<ApiResponse<void>> {
    return request.delete(`/principal/teachers/${teacherId}`)
  },

  /**
   * 获取所有老师列表（不分页）
   */
  getAll(subject?: string): Promise<ApiResponse<TeacherInfo[]>> {
    const params = subject ? { subject } : {}
    return request.get('/principal/teachers/all', params)
  },

  /**
   * 获取空闲教师列表
   */
  getAvailableTeachers(params: AvailableTeachersParams): Promise<ApiResponse<TeacherInfo[]>> {
    return request.get('/principal/teachers/available', params)
  }
}

/**
 * 管理员端老师管理API
 */
export const adminTeacherApi = {
  /**
   * 获取老师列表（包含校长信息）
   */
  getTeacherList(params: TeacherListParams): Promise<ApiResponse<PageResult<TeacherInfo>>> {
    return request.get('/admin/teachers', params)
  },

  /**
   * 根据ID获取老师详细信息
   */
  getTeacherById(teacherId: number): Promise<ApiResponse<TeacherInfo>> {
    return request.get(`/admin/teachers/${teacherId}`)
  },

  /**
   * 更新老师信息
   */
  updateTeacher(teacherId: number, data: UpdateTeacherData): Promise<ApiResponse<TeacherInfo>> {
    return request.put(`/admin/teachers/${teacherId}`, data)
  },

  /**
   * 修改老师与校长的关系
   */
  updateTeacherPrincipal(teacherId: number, principalId?: number): Promise<ApiResponse<void>> {
    const params = principalId ? { principalId } : {}
    return request.put(`/admin/teachers/${teacherId}/principal`, null, { params })
  }
}

/**
 * 老师端老师管理API
 */
export const teacherTeacherApi = {
  /**
   * 获取同校所有老师列表（不分页）
   */
  getAll(): Promise<ApiResponse<TeacherInfo[]>> {
    return request.get('/teacher/teachers/all')
  }
}
